
import { NextRequest } from 'next/server';

// Configuration for the external chatbot API
const CHATBOT_API_URL = 'http://localhost:5000/api/v1/chat';

// Generate auth code based on current time (matches Python backend logic)
function generateAuthCode(): string {
  const currentTime = new Date();
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();

  // Equivalent to Python: abs(math.sin(71+hour-minute))
  const randomPositiveNumber = Math.abs(Math.sin(71 + hour - minute));

  // Convert to string and extract characters 2-5 (equivalent to [2:5] in Python)
  return randomPositiveNumber.toString().substring(2, 5);
}

export async function POST(request: NextRequest) {
  try {
    // Parse the incoming request body
    const body = await request.json();
    const { message, threadId } = body;

    if (!message) {
      return new Response(
        JSON.stringify({ error: 'Message is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!threadId) {
      return new Response(
        JSON.stringify({ error: 'ThreadId is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Generate auth code for security
    const authCode = generateAuthCode();

    // Create FormData for the chatbot API
    const formData = new FormData();
    formData.append('query', message);        // User's prompt
    formData.append('threadId', threadId);    // Session UUID
    formData.append('auth_code', authCode);   // Time-based security token

    // Forward the request to the external chatbot API
    const response = await fetch(CHATBOT_API_URL, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Chatbot API responded with status: ${response.status}`);
    }

    // Check if the response has a body to stream
    if (!response.body) {
      throw new Error('No response body received from chatbot API');
    }

    // Create a new ReadableStream that passes through the chatbot's response
    const stream = new ReadableStream({
      start(controller) {
        const reader = response.body!.getReader();

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            // Pass the chunk through to our client
            controller.enqueue(value);
            return pump();
          });
        }

        return pump();
      },
    });

    // Return the streaming response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error) {
    console.error('Error in zema API route:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to communicate with chatbot API',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
