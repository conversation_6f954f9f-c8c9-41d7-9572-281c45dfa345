import { NextRequest } from 'next/server';

// Configuration for the external chatbot API
const CHATBOT_API_URL = 'http://localhost:5000/api/vi/chat';

export async function POST(request: NextRequest) {
  try {
    // Parse the incoming request body
    const body = await request.json();
    const { message } = body;

    if (!message) {
      return new Response(
        JSON.stringify({ error: 'Message is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Forward the request to the external chatbot API
    const response = await fetch(CHATBOT_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message }),
    });

    if (!response.ok) {
      throw new Error(`Chatbot API responded with status: ${response.status}`);
    }

    // Check if the response has a body to stream
    if (!response.body) {
      throw new Error('No response body received from chatbot API');
    }

    // Create a new ReadableStream that passes through the chatbot's response
    const stream = new ReadableStream({
      start(controller) {
        const reader = response.body!.getReader();

        function pump(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            // Pass the chunk through to our client
            controller.enqueue(value);
            return pump();
          });
        }

        return pump();
      },
    });

    // Return the streaming response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error) {
    console.error('Error in zema API route:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to communicate with chatbot API',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}