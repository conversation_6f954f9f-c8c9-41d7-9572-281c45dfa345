
import { NextRequest } from 'next/server';

// Configuration for the external chatbot API
const CHATBOT_API_URL = 'http://localhost:5000/api/v1/chat';

// Generate auth code based on current time (matches Python backend logic)
function generateAuthCode(): string {
  const currentTime = new Date();
  const hour = currentTime.getUTCHours();
  const minute = currentTime.getUTCMinutes();

  // Equivalent to Python: abs(math.sin(71+hour-minute))
  const randomPositiveNumber = Math.abs(Math.sin(71 + hour - minute));

  // Convert to string and extract characters 2-5 (equivalent to [2:5] in Python)
  return randomPositiveNumber.toString().substring(2, 5);
}

export async function POST(request: NextRequest) {
  try {
    // Parse the incoming request body
    const body = await request.json();
    const { message, threadId } = body;

    if (!message) {
      return new Response(
        JSON.stringify({ error: 'Message is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!threadId) {
      return new Response(
        JSON.stringify({ error: 'ThreadId is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Generate auth code for security
    const authCode = generateAuthCode();

    // Create FormData for the chatbot API
    const formData = new FormData();
    formData.append('query', message);        // User's prompt
    formData.append('thread_id', threadId);    // Session UUID
    formData.append('auth_code', authCode);   // Time-based security token

    // Forward the request to the external chatbot API
    const response = await fetch(CHATBOT_API_URL, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Chatbot API responded with status: ${response.status}`);
    }

    // Check if the response has a body to stream
    if (!response.body) {
      throw new Error('No response body received from chatbot API');
    }

    // Create a new ReadableStream that parses SSE and extracts message content
    const stream = new ReadableStream({
      async start(controller) {
        const reader = response.body!.getReader();
        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              controller.close();
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  if (data.message) {
                    // Send just the message content as plain text
                    const encoder = new TextEncoder();
                    controller.enqueue(encoder.encode(data.message));
                  }
                } catch (e) {
                  console.error('Error parsing SSE data:', e);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error reading stream:', error);
          controller.error(error);
        }
      },
    });

    // Return the streaming response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });

  } catch (error) {
    console.error('Error in zema API route:', error);

    return new Response(
      JSON.stringify({
        error: 'Failed to communicate with chatbot API',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
