import { API_ENDPOINT } from '../constants';

// Utility function to generate unique thread IDs
export function generateThreadId(): string {
  return Date.now().toString() + '-' + Math.random().toString(36).substring(2, 11);
}

export class ChatAPI {
  static async sendMessage(message: string, threadId: string): Promise<ReadableStream<Uint8Array> | null> {
    try {
      const response = await fetch(API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message, threadId }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.body;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  static async *streamResponse(stream: ReadableStream<Uint8Array>): AsyncGenerator<string, void, unknown> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        // The API route now sends plain text tokens directly
        const chunk = decoder.decode(value, { stream: true });
        if (chunk.trim()) {
          yield chunk;
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}
