'use client';

import { useState, useCallback } from 'react';
import { Message, ChatState } from '../types';
import { MESSAGE_TYPES } from '../constants';
import { ChatAPI } from '../utils/api';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

export default function ChatContainer() {
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    error: null,
  });

  const addMessage = useCallback((message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };
    
    setChatState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
    }));
    
    return newMessage.id;
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<Message>) => {
    setChatState(prev => ({
      ...prev,
      messages: prev.messages.map(msg => 
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  }, []);

  const handleSendMessage = useCallback(async (content: string) => {
    // Add user message
    addMessage({
      type: MESSAGE_TYPES.USER,
      content,
    });

    // Set loading state
    setChatState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Send message to API
      const stream = await ChatAPI.sendMessage(content);
      
      if (!stream) {
        throw new Error('No response stream received');
      }

      // Add assistant message with streaming
      const assistantMessageId = addMessage({
        type: MESSAGE_TYPES.ASSISTANT,
        content: '',
        isStreaming: true,
      });

      let fullContent = '';

      // Process streaming response
      for await (const token of ChatAPI.streamResponse(stream)) {
        fullContent += token;
        updateMessage(assistantMessageId, { content: fullContent });
      }

      // Mark streaming as complete
      updateMessage(assistantMessageId, { isStreaming: false });

    } catch (error) {
      console.error('Error sending message:', error);
      setChatState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'An error occurred',
      }));
      
      // Add error message
      addMessage({
        type: MESSAGE_TYPES.ASSISTANT,
        content: 'Sorry, I encountered an error while processing your message. Please try again.',
      });
    } finally {
      setChatState(prev => ({ ...prev, isLoading: false }));
    }
  }, [addMessage, updateMessage]);

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Messages */}
      <MessageList
        messages={chatState.messages}
        isLoading={chatState.isLoading}
      />

      {/* Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        disabled={chatState.isLoading}
      />
    </div>
  );
}
