const withMDX = require("@next/mdx")();

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configure `pageExtensions` to include MDX files
  pageExtensions: ["js", "jsx", "mdx", "ts", "tsx"],
  // Enable standalone output for Docker optimization
  output: 'standalone',
  // Optionally, add any other Next.js config below
  async redirects() {
    return [
      {
        source: "/frontend-developer-niederosterreich-wien-oberoesterreich/",
        destination: "/macht/webapps-und-webseiten",
        permanent: true, // Set to true for 301 permanent redirect
      },
      {
        source: "/seo-spezialist-niederosterreich-wien-und-oberosterreich/",
        destination: "/macht/seo/",
        permanent: true, // Set to true for 301 permanent redirect
      },
    ];
  },
};

module.exports = withMDX(nextConfig);
