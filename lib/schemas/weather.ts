import { z } from 'zod';

// Weather data schema for validation
export const WeatherDataSchema = z.object({
  timestamp: z.string().datetime(), // ISO 8601 format - required
  station_lat: z.number().min(-90).max(90), // Latitude - required
  station_long: z.number().min(-180).max(180), // Longitude - required
  temperature: z.number(), // Temperature in Celsius - required
  wind_speed_meter_per_seconds: z.number().min(0), // Wind speed in m/s - required
  is_raining: z.boolean(), // Boolean for rain status - required
  sun_lux: z.number().min(0), // Sun intensity in lux - required
  sun_twilight: z.number().min(0), // Twilight intensity as numeric value - required
});

// TypeScript type derived from schema
export type WeatherData = z.infer<typeof WeatherDataSchema>;

// Database record type (includes auto-generated fields)
export interface WeatherRecord extends WeatherData {
  id: number;
  created_at: string;
}

// Query parameters for GET requests
export const WeatherQuerySchema = z.object({
  station_lat: z.coerce.number().min(-90).max(90).optional().nullable(),
  station_long: z.coerce.number().min(-180).max(180).optional().nullable(),
  start_date: z.string().datetime().optional().nullable(),
  end_date: z.string().datetime().optional().nullable(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0),
});

export type WeatherQuery = z.infer<typeof WeatherQuerySchema>;

// Response types
export interface WeatherResponse {
  success: boolean;
  data?: WeatherRecord[];
  count?: number;
  message?: string;
  error?: string;
}

export interface WeatherCreateResponse {
  success: boolean;
  data?: WeatherRecord;
  message?: string;
  error?: string;
}
