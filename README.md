This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Docker Deployment

This project includes an optimized multi-stage Dockerfile for production deployment.

### Quick Start with Docker

1. **Build the image:**
   ```bash
   ./docker-build.sh
   ```

2. **Run with docker-compose (recommended):**
   ```bash
   docker-compose up -d
   ```

3. **Or run directly:**
   ```bash
   docker run -p 3000:3000 -v $(pwd)/data:/app/data julianhandl-website:latest
   ```

### Docker Features

- **Multi-stage build** for minimal image size
- **Non-root user** for security
- **Health checks** built-in
- **SQLite database** persistence via volume mount
- **Optimized** for production with Next.js standalone output

### Database Persistence

The SQLite database is stored in the `./data` directory and mounted as a volume. This ensures:
- Data survives container restarts
- Easy backups by copying the `data` directory
- Database files are excluded from the Docker image

### Environment Variables

- `NODE_ENV=production` (set automatically)
- `NEXT_TELEMETRY_DISABLED=1` (set automatically)
- `PORT=3000` (default, can be overridden)

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
