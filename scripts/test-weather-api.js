#!/usr/bin/env node

/**
 * Simple test script for the Weather API
 * Run with: node scripts/test-weather-api.js
 */

const BASE_URL = 'http://localhost:3000/api/data/weather';

async function testWeatherAPI() {
  console.log('🌤️  Testing Weather API...\n');

  try {
    // Test 1: Store weather data
    console.log('📤 Test 1: Storing weather data...');
    const weatherData = {
      timestamp: new Date().toISOString(),
      station_lat: 48.2082,
      station_long: 16.3738,
      temperature: 23.5,
      wind_speed_meter_per_seconds: 7.2,
      is_raining: false,
      sun_lux: 45000,
      sun_twilight: 15000
    };

    const postResponse = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(weatherData),
    });

    const postResult = await postResponse.json();
    
    if (postResult.success) {
      console.log('✅ Weather data stored successfully');
      console.log(`   Record ID: ${postResult.data.id}`);
      console.log(`   Location: ${postResult.data.station_lat}, ${postResult.data.station_long}`);
      console.log(`   Temperature: ${postResult.data.temperature}°C`);
      console.log(`   Wind Speed: ${postResult.data.wind_speed_meter_per_seconds} m/s\n`);
    } else {
      console.log('❌ Failed to store weather data');
      console.log('   Error:', postResult.error);
      return;
    }

    // Test 2: Retrieve all weather data
    console.log('📥 Test 2: Retrieving all weather data...');
    const getAllResponse = await fetch(`${BASE_URL}?limit=10`);
    const getAllResult = await getAllResponse.json();

    if (getAllResult.success) {
      console.log(`✅ Retrieved ${getAllResult.data.length} weather records`);
      console.log(`   Total count: ${getAllResult.count}`);
      console.log(`   Latest record location: ${getAllResult.data[0]?.station_lat || 'None'}, ${getAllResult.data[0]?.station_long || 'None'}\n`);
    } else {
      console.log('❌ Failed to retrieve weather data');
      console.log('   Error:', getAllResult.error);
    }

    // Test 3: Filter by station location
    console.log('🔍 Test 3: Filtering by station location...');
    const filterResponse = await fetch(`${BASE_URL}?station_lat=${weatherData.station_lat}&station_long=${weatherData.station_long}`);
    const filterResult = await filterResponse.json();

    if (filterResult.success) {
      console.log(`✅ Found ${filterResult.data.length} records for location ${weatherData.station_lat}, ${weatherData.station_long}`);
      if (filterResult.data.length > 0) {
        const record = filterResult.data[0];
        console.log(`   Temperature: ${record.temperature}°C`);
        console.log(`   Wind Speed: ${record.wind_speed_meter_per_seconds} m/s`);
        console.log(`   Is Raining: ${record.is_raining}`);
        console.log(`   Sun Lux: ${record.sun_lux}`);
        console.log(`   Sun Twilight: ${record.sun_twilight}\n`);
      }
    } else {
      console.log('❌ Failed to filter weather data');
      console.log('   Error:', filterResult.error);
    }

    // Test 4: Test validation with invalid data
    console.log('🚫 Test 4: Testing validation with invalid data...');
    const invalidData = {
      timestamp: 'invalid_timestamp',
      station_lat: 200, // Invalid: > 90
      station_long: -200, // Invalid: < -180
      temperature: 'not_a_number',
      wind_speed_meter_per_seconds: -5, // Invalid: negative
      is_raining: 'not_a_boolean',
      sun_lux: -1000, // Invalid: negative
      sun_twilight: -500 // Invalid: negative
    };

    const invalidResponse = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidData),
    });

    const invalidResult = await invalidResponse.json();
    
    if (!invalidResult.success && invalidResponse.status === 400) {
      console.log('✅ Validation correctly rejected invalid data');
      console.log(`   Error: ${invalidResult.error}\n`);
    } else {
      console.log('❌ Validation should have rejected invalid data');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('\n💡 Make sure the development server is running:');
    console.log('   npm run dev');
  }
}

// Run the tests
testWeatherAPI();
