#!/bin/bash

# Docker build script for <PERSON> website
# This script builds an optimized production Docker image

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="julianhandl-website"
TAG="${1:-latest}"
FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"

echo -e "${YELLOW}Building Docker image: ${FULL_IMAGE_NAME}${NC}"

# Ensure data directory exists
if [ ! -d "./data" ]; then
    echo -e "${YELLOW}Creating data directory...${NC}"
    mkdir -p ./data
fi

# Build the Docker image
echo -e "${YELLOW}Starting Docker build...${NC}"
docker build \
    --tag "${FULL_IMAGE_NAME}" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    .

# Check if build was successful
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker image built successfully: ${FULL_IMAGE_NAME}${NC}"
    
    # Show image size
    echo -e "${YELLOW}Image size:${NC}"
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
    
    echo -e "${GREEN}🚀 To run the container:${NC}"
    echo "docker run -p 3000:3000 -v \$(pwd)/data:/app/data ${FULL_IMAGE_NAME}"
    echo ""
    echo -e "${GREEN}🐳 Or use docker-compose:${NC}"
    echo "docker-compose up -d"
    
else
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
fi
