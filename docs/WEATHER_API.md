# Weather Data API

This API allows weather stations to store weather data and retrieve historical weather information.

## Base URL
```
http://localhost:3000/api/data/weather
```

## Endpoints

### POST /api/data/weather
Store weather data from a weather station.

#### Request Body
```json
{
  "timestamp": "2025-06-03T13:45:50.666Z",
  "station_lat": 48.2082,
  "station_long": 16.3738,
  "temperature": 22.5,
  "wind_speed_meter_per_seconds": 5.8,
  "is_raining": false,
  "sun_lux": 45000,
  "sun_twilight": 15000
}
```

#### Field Descriptions
- `timestamp` (string, required): ISO 8601 datetime
- `station_lat` (number, required): Station latitude (-90 to 90)
- `station_long` (number, required): Station longitude (-180 to 180)
- `temperature` (number, required): Temperature in Celsius
- `wind_speed_meter_per_seconds` (number, required): Wind speed in meters per second (≥ 0)
- `is_raining` (boolean, required): Whether it's currently raining
- `sun_lux` (number, required): Sun intensity in lux (≥ 0)
- `sun_twilight` (number, required): Twilight intensity as numeric value (≥ 0)

#### Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "timestamp": "2025-06-03T13:45:50.666Z",
    "station_lat": 48.2082,
    "station_long": 16.3738,
    "temperature": 22.5,
    "wind_speed_meter_per_seconds": 5.8,
    "is_raining": false,
    "sun_lux": 45000,
    "sun_twilight": 15000,
    "created_at": "2025-06-03 13:45:50"
  },
  "message": "Weather data stored successfully"
}
```

### GET /api/data/weather
Retrieve weather data with optional filtering and pagination.

#### Query Parameters
- `station_lat` (number, optional): Filter by station latitude
- `station_long` (number, optional): Filter by station longitude
- `start_date` (string, optional): Filter records after this date (ISO 8601)
- `end_date` (string, optional): Filter records before this date (ISO 8601)
- `limit` (number, optional): Maximum number of records to return (1-1000, default: 100)
- `offset` (number, optional): Number of records to skip (default: 0)

#### Examples
```bash
# Get all weather data
GET /api/data/weather

# Get data from specific station location
GET /api/data/weather?station_lat=48.2082&station_long=16.3738

# Get recent data with pagination
GET /api/data/weather?limit=10&offset=0

# Get data within date range
GET /api/data/weather?start_date=2025-06-01T00:00:00Z&end_date=2025-06-03T23:59:59Z
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "id": 2,
      "timestamp": "2025-06-03T13:46:47.700Z",
      "station_lat": 48.2082,
      "station_long": 16.3738,
      "temperature": 18.3,
      "wind_speed_meter_per_seconds": 12.2,
      "is_raining": true,
      "sun_lux": 25000,
      "sun_twilight": 8000,
      "created_at": "2025-06-03 13:46:47"
    }
  ],
  "count": 1,
  "message": "Retrieved 1 weather records"
}
```

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "error": "Invalid weather data format",
  "details": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["temperature"],
      "message": "Expected number, received string"
    }
  ]
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to store weather data",
  "message": "Database connection error"
}
```

## Database Schema

The weather data is stored in SQLite with the following schema:

```sql
CREATE TABLE weather_data (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  timestamp TEXT NOT NULL,
  station_lat REAL NOT NULL,
  station_long REAL NOT NULL,
  temperature REAL NOT NULL,
  wind_speed_meter_per_seconds REAL NOT NULL,
  is_raining INTEGER NOT NULL,
  sun_lux REAL NOT NULL,
  sun_twilight REAL NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**Note:** The `is_raining` field is stored as INTEGER (0/1) in SQLite but converted to boolean in API responses. The `sun_twilight` field is stored and returned as a numeric value.

## Testing

You can test the API using curl:

```bash
# Store weather data
curl -X POST http://localhost:3000/api/data/weather \
  -H "Content-Type: application/json" \
  -d '{
    "timestamp": "2025-06-03T14:30:00.000Z",
    "station_lat": 48.2082,
    "station_long": 16.3738,
    "temperature": 25.0,
    "wind_speed_meter_per_seconds": 3.5,
    "is_raining": false,
    "sun_lux": 50000,
    "sun_twilight": 15000
  }'

# Retrieve weather data
curl -X GET "http://localhost:3000/api/data/weather?limit=5"

# Get data from specific location
curl -X GET "http://localhost:3000/api/data/weather?station_lat=48.2082&station_long=16.3738"
```

## Database Location

The SQLite database is stored in `./data/weather.db` relative to the project root. This ensures:
- **Production compatibility**: Database persists across deployments
- **Data separation**: Database files are in a dedicated directory
- **Backup friendly**: Easy to backup the entire `data` directory
